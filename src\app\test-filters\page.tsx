"use client";

import { useState } from 'react';
import { AmenityFilter } from '@/components/ui/amenity-filter';
import { PriceFilter } from '@/components/ui/price-filter';
import { LocationFilter } from '@/components/ui/location-filter';
import { AreaFilter } from '@/components/ui/area-filter';
import { SearchFilters } from '@/components/ui/search-filters';
import { SearchInputWithFilters } from '@/components/ui/search-input-with-filters';

export default function TestFiltersPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);
  const [selectedPrices, setSelectedPrices] = useState<string[]>([]);
  const [selectedLocation, setSelectedLocation] = useState<string>('');
  const [selectedAreas, setSelectedAreas] = useState<string[]>([]);

  const handleSearch = () => {
    console.log('Search:', { searchQuery, selectedAmenities, selectedPrices, selectedLocation, selectedAreas });
  };

  const handleRemoveFilter = (type: string, value: string) => {
    switch (type) {
      case 'amenity':
        setSelectedAmenities(prev => prev.filter(id => id !== value));
        break;
      case 'price':
        setSelectedPrices(prev => prev.filter(id => id !== value));
        break;
      case 'location':
        setSelectedLocation('');
        break;
      case 'area':
        setSelectedAreas(prev => prev.filter(id => id !== value));
        break;
    }
  };

  return (
    <div className="container mx-auto p-8 pt-20 space-y-8">
      <h1 className="text-2xl font-bold text-gray-900">Test Filter Components</h1>

      {/* Test Search Input with Filters */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-800">Search Input with Selected Filters</h2>
        <SearchInputWithFilters
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          onSearch={handleSearch}
          selectedAmenities={selectedAmenities}
          selectedPrices={selectedPrices}
          selectedLocation={selectedLocation}
          selectedAreas={selectedAreas}
          onRemoveFilter={handleRemoveFilter}
        />
      </div>

      {/* Test Individual Filters */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Test Location Filter */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-800">Location Filter</h2>
          <LocationFilter
            selectedLocation={selectedLocation}
            onLocationChange={setSelectedLocation}
          />
        </div>

        {/* Test Area Filter */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-800">Area Filter</h2>
          <AreaFilter
            selectedAreas={selectedAreas}
            onSelectionChange={setSelectedAreas}
          />
        </div>

        {/* Test Amenity Filter */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-800">Amenity Filter</h2>
          <AmenityFilter
            selectedAmenities={selectedAmenities}
            onSelectionChange={setSelectedAmenities}
          />
        </div>

        {/* Test Price Filter */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-800">Price Filter</h2>
          <PriceFilter
            selectedPrices={selectedPrices}
            onSelectionChange={setSelectedPrices}
          />
        </div>
      </div>

      {/* Test All Filters Combined */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-800">Complete Search Filters</h2>
        <SearchFilters />
      </div>

      {/* Navigation Test */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-800">Navigation Test</h2>
        <p className="text-sm text-gray-600">
          Kiểm tra navigation bar ở trên trang để xem filter mới đã được áp dụng.
          Các filter sẽ hiển thị selected items trong search input và có category cho amenities.
        </p>
      </div>

      {/* Debug Info */}
      <div className="mt-8 p-4 bg-gray-100 rounded-lg">
        <h3 className="text-sm font-medium text-gray-700 mb-2">Current Selection:</h3>
        <div className="text-sm text-gray-600 space-y-1">
          <div><strong>Search:</strong> {searchQuery || 'None'}</div>
          <div><strong>Location:</strong> {selectedLocation || 'None'}</div>
          <div><strong>Amenities:</strong> {selectedAmenities.length > 0 ? selectedAmenities.join(', ') : 'None'}</div>
          <div><strong>Prices:</strong> {selectedPrices.length > 0 ? selectedPrices.join(', ') : 'None'}</div>
          <div><strong>Areas:</strong> {selectedAreas.length > 0 ? selectedAreas.join(', ') : 'None'}</div>
        </div>
      </div>
    </div>
  );
}
