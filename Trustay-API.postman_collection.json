{"info": {"_postman_id": "trustay-api-collection", "name": "Trustay API Collection", "description": "Complete API collection for Trustay rental platform with authentication flows and refresh token support", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "trustay-team"}, "item": [{"name": "🔐 Authentication", "item": [{"name": "📧 Send Email Verification", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('verificationId', response.verificationId);", "    console.log('📧 Verification ID saved:', response.verificationId);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"email\",\n  \"email\": \"{{testEmail}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/verification/send", "host": ["{{baseUrl}}"], "path": ["api", "verification", "send"]}, "description": "Send verification code to email address"}, "response": []}, {"name": "✅ Verify Email Code", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('verificationToken', response.verificationToken);", "    console.log('✅ Verification Token saved:', response.verificationToken);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"email\",\n  \"email\": \"{{testEmail}}\",\n  \"code\": \"{{verificationCode}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/verification/verify", "host": ["{{baseUrl}}"], "path": ["api", "verification", "verify"]}, "description": "Verify email with 6-digit code"}, "response": []}, {"name": "📝 Register with Verification", "event": [{"listen": "pre-request", "script": {"exec": ["// Set verification token in header", "const verificationToken = pm.collectionVariables.get('verificationToken');", "if (verificationToken) {", "    pm.request.headers.add({", "        key: 'X-Verification-Token',", "        value: verificationToken", "    });", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('accessToken', response.access_token);", "    pm.collectionVariables.set('refreshToken', response.refresh_token);", "    pm.collectionVariables.set('userId', response.user.id);", "    console.log('✅ Registration successful!');", "    console.log('🔑 Access Token saved:', response.access_token.substring(0, 20) + '...');", "    console.log('🔄 Refresh Token saved:', response.refresh_token ? response.refresh_token.substring(0, 20) + '...' : 'Not provided');", "    console.log('👤 User ID saved:', response.user.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{testEmail}}\",\n  \"password\": \"{{testPassword}}\",\n  \"firstName\": \"{{testFirstName}}\",\n  \"lastName\": \"{{testLastName}}\",\n  \"phone\": \"{{testPhone}}\",\n  \"gender\": \"{{testGender}}\",\n  \"role\": \"{{testRole}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}, "description": "Complete registration with verified email/phone"}, "response": []}, {"name": "⚡ Register Direct (Development)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('accessToken', response.access_token);", "    pm.collectionVariables.set('refreshToken', response.refresh_token);", "    pm.collectionVariables.set('userId', response.user.id);", "    console.log('✅ Direct Registration successful!');", "    console.log('🔑 Access Token saved:', response.access_token.substring(0, 20) + '...');", "    console.log('🔄 Refresh Token saved:', response.refresh_token ? response.refresh_token.substring(0, 20) + '...' : 'Not provided');", "    console.log('👤 User ID saved:', response.user.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{testEmailDev}}\",\n  \"password\": \"{{testPassword}}\",\n  \"firstName\": \"{{testFirstName}}\",\n  \"lastName\": \"{{testLastName}}\",\n  \"phone\": \"{{testPhoneDev}}\",\n  \"gender\": \"{{testGender}}\",\n  \"role\": \"{{testRole}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register-direct", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register-direct"]}, "description": "Direct registration for development (bypasses verification)"}, "response": []}, {"name": "🔑 Login", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('accessToken', response.access_token);", "    pm.collectionVariables.set('refreshToken', response.refresh_token);", "    pm.collectionVariables.set('userId', response.user.id);", "    console.log('✅ Login successful!');", "    console.log('🔑 Access Token saved:', response.access_token.substring(0, 20) + '...');", "    console.log('🔄 Refresh Token saved:', response.refresh_token ? response.refresh_token.substring(0, 20) + '...' : 'Not provided');", "    console.log('👤 User ID saved:', response.user.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{loginEmail}}\",\n  \"password\": \"{{loginPassword}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}, "description": "Login with email and password"}, "response": []}, {"name": "👤 Get Current User", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/me", "host": ["{{baseUrl}}"], "path": ["api", "auth", "me"]}, "description": "Get current authenticated user profile (auto-auth enabled)"}, "response": []}, {"name": "🔄 Refresh <PERSON>ken", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('accessToken', response.access_token);", "    pm.collectionVariables.set('refreshToken', response.refresh_token);", "    console.log('✅ Token refreshed successfully!');", "    console.log('🔑 New Access Token saved:', response.access_token.substring(0, 20) + '...');", "    console.log('🔄 New Refresh Token saved:', response.refresh_token.substring(0, 20) + '...');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refreshToken}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/refresh", "host": ["{{baseUrl}}"], "path": ["api", "auth", "refresh"]}, "description": "Refresh access token using refresh token"}, "response": []}, {"name": "🧪 Test Auth Flow", "event": [{"listen": "test", "script": {"exec": ["// Test that auto-auth is working", "if (pm.response.code === 200) {", "    console.log('✅ Auto-authentication working correctly!');", "    const response = pm.response.json();", "    console.log('Current user:', response.firstName, response.lastName);", "} else if (pm.response.code === 401) {", "    console.log('❌ Authentication failed. Try refreshing token or login again.');", "} else {", "    console.log('⚠️ Unexpected response:', pm.response.code);", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/me", "host": ["{{baseUrl}}"], "path": ["api", "auth", "me"]}, "description": "Test endpoint to verify automatic authentication headers are working"}, "response": []}, {"name": "🔒 Change Password", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"{{currentPassword}}\",\n  \"newPassword\": \"{{newPassword}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/change-password", "host": ["{{baseUrl}}"], "path": ["api", "auth", "change-password"]}, "description": "Change user password (auto-auth enabled)"}, "response": []}, {"name": "🔍 Check Password Strength", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"password\": \"{{testPasswordToCheck}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/check-password-strength", "host": ["{{baseUrl}}"], "path": ["api", "auth", "check-password-strength"]}, "description": "Check password strength and security score"}, "response": []}, {"name": "🎲 Generate Secure Password", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/generate-password?length={{passwordLength}}", "host": ["{{baseUrl}}"], "path": ["api", "auth", "generate-password"], "query": [{"key": "length", "value": "{{passwordLength}}"}]}, "description": "Generate a secure password with specified length"}, "response": []}, {"name": "🗑️ Revoke Refresh Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refreshToken}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/revoke", "host": ["{{baseUrl}}"], "path": ["api", "auth", "revoke"]}, "description": "Revoke a specific refresh token"}, "response": []}, {"name": "🗑️ Revoke All Refresh Tokens", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    console.log('✅ All refresh tokens revoked successfully!');", "    // Clear refresh token from collection variables", "    pm.collectionVariables.set('refreshToken', '');", "    console.log('🔄 Refresh token cleared from collection variables.');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/revoke-all", "host": ["{{baseUrl}}"], "path": ["api", "auth", "revoke-all"]}, "description": "Revoke all refresh tokens for current user (auto-auth enabled)"}, "response": []}, {"name": "🚪 Logout (Clear All Tokens)", "event": [{"listen": "pre-request", "script": {"exec": ["// Try to revoke refresh token before clearing", "const refreshToken = pm.collectionVariables.get('refreshToken');", "if (refreshToken) {", "    console.log('🔓 Attempting to revoke refresh token before logout...');", "} else {", "    console.log('⚠️ No refresh token to revoke');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// Clear all authentication tokens", "pm.collectionVariables.set('accessToken', '');", "pm.collectionVariables.set('refreshToken', '');", "pm.collectionVariables.set('userId', '');", "pm.collectionVariables.set('verificationToken', '');", "pm.collectionVariables.set('verificationId', '');", "", "console.log('🚪 <PERSON>gout completed - All tokens cleared');", "console.log('You can now register/login with new credentials.');"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refreshToken}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/revoke", "host": ["{{baseUrl}}"], "path": ["api", "auth", "revoke"]}, "description": "Logout user by revoking refresh token and clearing all stored tokens"}, "response": []}], "description": "Complete authentication flow with refresh token support and automatic header management"}, {"name": "👤 User Management", "item": [{"name": "Get User Profile", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users/profile", "host": ["{{baseUrl}}"], "path": ["api", "users", "profile"]}, "description": "Get authenticated user's profile (auto-auth enabled)"}, "response": []}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"{{updatedFirstName}}\",\n  \"lastName\": \"{{updatedLastName}}\",\n  \"phone\": \"{{updatedPhone}}\",\n  \"gender\": \"{{updatedGender}}\",\n  \"bio\": \"{{updatedBio}}\",\n  \"dateOfBirth\": \"{{updatedDateOfBirth}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users/profile", "host": ["{{baseUrl}}"], "path": ["api", "users", "profile"]}, "description": "Update user profile information (auto-auth enabled)"}, "response": []}, {"name": "📍 Create User Address", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"{{addressType}}\",\n  \"street\": \"{{addressStreet}}\",\n  \"wardId\": {{addressWardId}},\n  \"districtId\": {{addressDistrictId}},\n  \"provinceId\": {{addressProvinceId}},\n  \"isDefault\": {{addressIsDefault}}\n}"}, "url": {"raw": "{{baseUrl}}/api/users/addresses", "host": ["{{baseUrl}}"], "path": ["api", "users", "addresses"]}, "description": "Create a new address for the user (auto-auth enabled)"}, "response": []}, {"name": "📍 Update User Address", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"{{updatedAddressType}}\",\n  \"street\": \"{{updatedAddressStreet}}\",\n  \"wardId\": {{updatedAddressWardId}},\n  \"districtId\": {{updatedAddressDistrictId}},\n  \"provinceId\": {{updatedAddressProvinceId}},\n  \"isDefault\": {{updatedAddressIsDefault}}\n}"}, "url": {"raw": "{{baseUrl}}/api/users/addresses/{{addressId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "addresses", "{{addressId}}"]}, "description": "Update an existing user address (auto-auth enabled)"}, "response": []}, {"name": "🗑️ Delete User Address", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/users/addresses/{{addressId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "addresses", "{{addressId}}"]}, "description": "Delete a user address (auto-auth enabled)"}, "response": []}, {"name": "📱 Verify Phone Number", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"{{phoneToVerify}}\",\n  \"code\": \"{{phoneVerificationCode}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users/verify-phone", "host": ["{{baseUrl}}"], "path": ["api", "users", "verify-phone"]}, "description": "Verify user phone number with verification code (auto-auth enabled)"}, "response": []}, {"name": "📧 Verify Email Address", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{emailToVerify}}\",\n  \"code\": \"{{emailVerificationCode}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users/verify-email", "host": ["{{baseUrl}}"], "path": ["api", "users", "verify-email"]}, "description": "Verify user email address with verification code (auto-auth enabled)"}, "response": []}, {"name": "🆔 Verify Identity", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"idCardNumber\": \"{{idCardNumber}}\",\n  \"fullName\": \"{{idCardFullName}}\",\n  \"dateOfBirth\": \"{{idCardDateOfBirth}}\",\n  \"address\": \"{{idCardAddress}}\",\n  \"issueDate\": \"{{idCardIssueDate}}\",\n  \"issuePlace\": \"{{idCardIssuePlace}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users/verify-identity", "host": ["{{baseUrl}}"], "path": ["api", "users", "verify-identity"]}, "description": "Verify user identity with ID card information (auto-auth enabled)"}, "response": []}, {"name": "🖼️ Upload/Update Avatar", "request": {"method": "PUT", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Avatar image file (max 10MB)"}]}, "url": {"raw": "{{baseUrl}}/api/users/avatar", "host": ["{{baseUrl}}"], "path": ["api", "users", "avatar"]}, "description": "Upload or update user avatar image (auto-auth enabled)"}, "response": []}], "description": "User profile management endpoints with automatic authentication"}, {"name": "📤 Upload Services", "item": [{"name": "📷 Upload Single Image", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200 || pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.url) {", "        // Extract image path from URL", "        const imagePath = response.url.replace(/.*\\/images\\//, '');", "        pm.collectionVariables.set('imagePath', imagePath);", "        console.log('📷 Image uploaded successfully!');", "        console.log('🔗 Image URL:', response.url);", "        console.log('📁 Image path saved:', imagePath);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Image file to upload (max 10MB)"}, {"key": "altText", "value": "{{imageAltText}}", "type": "text", "description": "Alt text for the image"}]}, "url": {"raw": "{{baseUrl}}/api/upload", "host": ["{{baseUrl}}"], "path": ["api", "upload"]}, "description": "Upload a single image file"}, "response": []}, {"name": "📷 Upload Multiple Images", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200 || pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.results && response.results.length > 0) {", "        console.log('📷 Multiple images uploaded successfully!');", "        console.log('📊 Total images:', response.total);", "        response.results.forEach((result, index) => {", "            console.log(`🔗 Image ${index + 1} URL:`, result.url);", "        });", "        // Save first image path for testing", "        const firstImagePath = response.results[0].url.replace(/.*\\/images\\//, '');", "        pm.collectionVariables.set('imagePath', firstImagePath);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": [], "description": "Multiple image files (max 10 files, 10MB each)"}, {"key": "altTexts", "value": "[\"{{imageAltText1}}\", \"{{imageAltText2}}\"]", "type": "text", "description": "Array of alt texts for the images"}]}, "url": {"raw": "{{baseUrl}}/api/upload/bulk", "host": ["{{baseUrl}}"], "path": ["api", "upload", "bulk"]}, "description": "Upload multiple image files at once (max 10 files)"}, "response": []}], "description": "Image upload services for avatars and property photos"}, {"name": "📊 Reference Data", "item": [{"name": "📋 Get All Enums", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/reference/enums", "host": ["{{baseUrl}}"], "path": ["api", "reference", "enums"]}, "description": "Get all enum values for dropdowns and validation"}, "response": []}, {"name": "🏠 Get Amenities", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/reference/amenities?category={{amenityCategory}}", "host": ["{{baseUrl}}"], "path": ["api", "reference", "amenities"], "query": [{"key": "category", "value": "{{amenityCategory}}", "description": "Filter by category (basic, kitchen, bathroom, entertainment, safety, connectivity, building)"}]}, "description": "Get all amenities with optional category filter"}, "response": []}, {"name": "💰 Get Cost Types", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/reference/cost-types?category={{costTypeCategory}}", "host": ["{{baseUrl}}"], "path": ["api", "reference", "cost-types"], "query": [{"key": "category", "value": "{{costTypeCategory}}", "description": "Filter by category (utility, service, parking, maintenance)"}]}, "description": "Get all cost types with optional category filter"}, "response": []}, {"name": "📜 Get Room Rules", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/reference/rules?category={{ruleCategory}}", "host": ["{{baseUrl}}"], "path": ["api", "reference", "rules"], "query": [{"key": "category", "value": "{{ruleCategory}}", "description": "Filter by category (smoking, pets, visitors, noise, cleanliness, security, usage, other)"}]}, "description": "Get all room rules with optional category filter"}, "response": []}], "description": "Reference data for amenities, cost types, rules, and enums"}, {"name": "🏠 Listings & Rooms", "item": [{"name": "🔍 Search Room Listings", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/listings/rooms?search={{searchKeyword}}&provinceId={{searchProvinceId}}&districtId={{searchDistrictId}}&wardId={{searchWardId}}&roomType={{searchRoomType}}&minPrice={{searchMinPrice}}&maxPrice={{searchMaxPrice}}&minArea={{searchMinArea}}&maxArea={{searchMaxArea}}&amenities={{searchAmenities}}&maxOccupancy={{searchMaxOccupancy}}&isVerified={{searchIsVerified}}&sortBy={{searchSortBy}}&sortOrder={{searchSortOrder}}&page={{searchPage}}&limit={{searchLimit}}", "host": ["{{baseUrl}}"], "path": ["api", "listings", "rooms"], "query": [{"key": "search", "value": "{{searchKeyword}}", "description": "Search keyword for room/building name"}, {"key": "provinceId", "value": "{{searchProvinceId}}", "description": "Filter by province ID"}, {"key": "districtId", "value": "{{searchDistrictId}}", "description": "Filter by district ID"}, {"key": "wardId", "value": "{{searchWardId}}", "description": "Filter by ward ID"}, {"key": "roomType", "value": "{{searchRoomType}}", "description": "Filter by room type"}, {"key": "minPrice", "value": "{{searchMinPrice}}", "description": "Minimum monthly rent"}, {"key": "maxPrice", "value": "{{searchMaxPrice}}", "description": "Maximum monthly rent"}, {"key": "minArea", "value": "{{searchMinArea}}", "description": "Minimum area in sqm"}, {"key": "maxArea", "value": "{{searchMaxArea}}", "description": "Maximum area in sqm"}, {"key": "amenities", "value": "{{searchAmenities}}", "description": "Required amenity IDs (comma-separated)"}, {"key": "maxOccupancy", "value": "{{searchMaxOccupancy}}", "description": "Maximum occupancy"}, {"key": "isVerified", "value": "{{searchIsVerified}}", "description": "Filter verified rooms only"}, {"key": "sortBy", "value": "{{searchSortBy}}", "description": "Sort field (price, area, createdAt)"}, {"key": "sortOrder", "value": "{{searchSortOrder}}", "description": "Sort order (asc, desc)"}, {"key": "page", "value": "{{searchPage}}", "description": "Page number"}, {"key": "limit", "value": "{{searchLimit}}", "description": "Items per page"}]}, "description": "Search and filter rental rooms with advanced filtering options"}, "response": []}, {"name": "🏠 Get Room Details by Slug", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/rooms/{{roomSlug}}", "host": ["{{baseUrl}}"], "path": ["api", "rooms", "{{roomSlug}}"]}, "description": "Get detailed information about a specific room using its slug"}, "response": []}], "description": "Room listings and detailed room information"}, {"name": "🌍 Location Services", "item": [{"name": "Get All Provinces", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/provinces", "host": ["{{baseUrl}}"], "path": ["api", "provinces"]}, "description": "Get list of all provinces in Vietnam"}, "response": []}, {"name": "Get Districts by Province", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/districts?provinceId={{testProvinceId}}", "host": ["{{baseUrl}}"], "path": ["api", "districts"], "query": [{"key": "provinceId", "value": "{{testProvinceId}}"}]}, "description": "Get districts within a specific province"}, "response": []}, {"name": "Get Wards by District", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/wards?districtId={{testDistrictId}}", "host": ["{{baseUrl}}"], "path": ["api", "wards"], "query": [{"key": "districtId", "value": "{{testDistrictId}}"}]}, "description": "Get wards within a specific district"}, "response": []}, {"name": "🔍 Search Addresses", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/address/search?query={{addressSearchQuery}}", "host": ["{{baseUrl}}"], "path": ["api", "address", "search"], "query": [{"key": "query", "value": "{{addressSearchQuery}}", "description": "Search term (minimum 2 characters)"}]}, "description": "Search addresses by query across provinces, districts, and wards"}, "response": []}], "description": "Vietnam location data endpoints (public access)"}, {"name": "🔧 Utilities", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/", "host": ["{{baseUrl}}"], "path": [""]}, "description": "API health check endpoint"}, "response": []}, {"name": "🏥 Detailed Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}, "description": "Detailed health status for monitoring"}, "response": []}, {"name": "📖 API Documentation", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api", "host": ["{{baseUrl}}"], "path": ["api"]}, "description": "Basic API information"}, "response": []}, {"name": "📚 Swagger Documentation", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/docs", "host": ["{{baseUrl}}"], "path": ["api", "docs"]}, "description": "Interactive Swagger API documentation"}, "response": []}, {"name": "🖼️ Get Uploaded Image", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/images/{{imagePath}}", "host": ["{{baseUrl}}"], "path": ["images", "{{imagePath}}"]}, "description": "Access uploaded images via static file serving"}, "response": []}], "description": "Utility and documentation endpoints"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Global pre-request script", "console.log('🚀 Trustay API Request:', pm.info.requestName);", "", "// Set common headers", "pm.request.headers.add({", "    key: 'User-Agent',", "    value: 'Trustay-Postman-Collection/2.0'", "});", "", "// Log current environment", "console.log('Environment:', pm.collectionVariables.get('environment'));", "", "// Auto-refresh token for protected endpoints", "const requestName = pm.info.requestName;", "const requestUrl = pm.request.url.toString();", "", "// Define endpoints that DON'T need authentication", "const publicEndpoints = [", "    'Login', 'Register', 'Verification', 'Password Strength', 'Generate', 'Health', 'Documentation',", "    'Provinces', 'Districts', 'Wards', 'Search Addresses', 'Get All Enums', 'Get Amenities',", "    'Get Cost Types', 'Get Room Rules', 'Search Room Listings', 'Get Room Details',", "    'Swagger Documentation', 'Get Uploaded Image'", "];", "", "const isPublicEndpoint = publicEndpoints.some(endpoint => requestName.includes(endpoint));", "const isRefreshEndpoint = requestName.includes('Refresh') || requestName.includes('Revoke');", "", "// Add auth header to protected endpoints (including /api/auth/me)", "if (!isPublicEndpoint && !isRefreshEndpoint) {", "    const accessToken = pm.collectionVariables.get('accessToken');", "    const refreshToken = pm.collectionVariables.get('refreshToken');", "    ", "    if (accessToken && !pm.request.headers.has('Authorization')) {", "        pm.request.headers.add({", "            key: 'Authorization',", "            value: `Bearer ${accessToken}`", "        });", "        console.log('🔐 Auto-added Authorization header for:', requestName);", "    } else if (pm.request.headers.has('Authorization')) {", "        console.log('🔐 Authorization header already present for:', requestName);", "    }", "    ", "    if (!accessToken && refreshToken) {", "        console.log('⚠️ No access token but refresh token available. Consider refreshing token first.');", "    }", "    ", "    if (!accessToken && !refreshToken) {", "        console.log('❌ No authentication tokens available. Please login first.');", "    }", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test script", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test('Response has proper headers', function () {", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('application/json');", "});", "", "// Log response for debugging", "if (pm.response.code >= 400) {", "    console.log('❌ Error Response:', pm.response.json());", "} else {", "    console.log('✅ Success Response');", "}"]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string", "description": "Base URL for Trustay API"}, {"key": "environment", "value": "development", "type": "string", "description": "Current environment (development/production)"}, {"key": "testEmail", "value": "<EMAIL>", "type": "string", "description": "Test email for verification"}, {"key": "testEmailDev", "value": "<EMAIL>", "type": "string", "description": "Test email for direct registration"}, {"key": "testPhone", "value": "+84901234567", "type": "string", "description": "Test phone number"}, {"key": "testPhoneDev", "value": "+84987654321", "type": "string", "description": "Test phone for direct registration"}, {"key": "testPassword", "value": "TrustayTest123!", "type": "string", "description": "Test password for registration"}, {"key": "testFirstName", "value": "<PERSON>", "type": "string", "description": "Test first name"}, {"key": "testLastName", "value": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "description": "Test last name"}, {"key": "testGender", "value": "male", "type": "string", "description": "Test gender (male/female/other)"}, {"key": "testRole", "value": "tenant", "type": "string", "description": "Test user role (tenant/landlord)"}, {"key": "verificationCode", "value": "123456", "type": "string", "description": "6-digit verification code (check console/email)"}, {"key": "loginEmail", "value": "<EMAIL>", "type": "string", "description": "Email for login"}, {"key": "loginPassword", "value": "TrustayTest123!", "type": "string", "description": "Password for login"}, {"key": "currentPassword", "value": "TrustayTest123!", "type": "string", "description": "Current password for change"}, {"key": "newPassword", "value": "NewTrustayPass456!", "type": "string", "description": "New password for change"}, {"key": "testPasswordToCheck", "value": "WeakPass", "type": "string", "description": "Password to check strength"}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "16", "type": "string", "description": "Length for generated password"}, {"key": "updatedFirstName", "value": "Minh Updated", "type": "string", "description": "Updated first name"}, {"key": "updatedLastName", "value": "<PERSON><PERSON><PERSON>n Updated", "type": "string", "description": "Updated last name"}, {"key": "updatedPhone", "value": "+84909999999", "type": "string", "description": "Updated phone number"}, {"key": "updatedGender", "value": "female", "type": "string", "description": "Updated gender"}, {"key": "updatedBio", "value": "I'm a Trustay user looking for the perfect rental.", "type": "string", "description": "Updated bio"}, {"key": "updatedDateOfBirth", "value": "1995-06-15", "type": "string", "description": "Updated date of birth (YYYY-MM-DD)"}, {"key": "testProvinceId", "value": "79", "type": "string", "description": "Test province ID (Ho Chi Minh City)"}, {"key": "testDistrictId", "value": "760", "type": "string", "description": "Test district ID (District 1)"}, {"key": "accessToken", "value": "", "type": "string", "description": "JWT access token (auto-populated)"}, {"key": "refreshToken", "value": "", "type": "string", "description": "JWT refresh token (auto-populated)"}, {"key": "verificationToken", "value": "", "type": "string", "description": "Verification token (auto-populated)"}, {"key": "verificationId", "value": "", "type": "string", "description": "Verification ID (auto-populated)"}, {"key": "userId", "value": "", "type": "string", "description": "Current user ID (auto-populated)"}, {"key": "addressType", "value": "home", "type": "string", "description": "Address type (home, work, other)"}, {"key": "addressStreet", "value": "123 Nguyen Hue Street", "type": "string", "description": "Street address"}, {"key": "addressWardId", "value": "26734", "type": "string", "description": "Ward ID for address"}, {"key": "addressDistrictId", "value": "760", "type": "string", "description": "District ID for address"}, {"key": "addressProvinceId", "value": "79", "type": "string", "description": "Province ID for address"}, {"key": "addressIsDefault", "value": "true", "type": "string", "description": "Whether this is the default address"}, {"key": "addressId", "value": "", "type": "string", "description": "Address ID for update/delete operations (auto-populated)"}, {"key": "updatedAddressType", "value": "work", "type": "string", "description": "Updated address type"}, {"key": "updatedAddressStreet", "value": "456 Le Loi Boulevard", "type": "string", "description": "Updated street address"}, {"key": "updatedAddressWardId", "value": "26734", "type": "string", "description": "Updated ward ID"}, {"key": "updatedAddressDistrictId", "value": "760", "type": "string", "description": "Updated district ID"}, {"key": "updatedAddressProvinceId", "value": "79", "type": "string", "description": "Updated province ID"}, {"key": "updatedAddressIsDefault", "value": "false", "type": "string", "description": "Updated default address status"}, {"key": "phoneToVerify", "value": "+84901234567", "type": "string", "description": "Phone number to verify"}, {"key": "phoneVerificationCode", "value": "123456", "type": "string", "description": "Phone verification code"}, {"key": "emailToVerify", "value": "<EMAIL>", "type": "string", "description": "Email address to verify"}, {"key": "emailVerificationCode", "value": "123456", "type": "string", "description": "Email verification code"}, {"key": "idCardNumber", "value": "123456789012", "type": "string", "description": "ID card number for identity verification"}, {"key": "idCardFullName", "value": "<PERSON><PERSON><PERSON>", "type": "string", "description": "Full name on ID card"}, {"key": "idCardDateOfBirth", "value": "1995-06-15", "type": "string", "description": "Date of birth on ID card (YYYY-MM-DD)"}, {"key": "idCardAddress", "value": "123 Nguyen Hue Street, District 1, Ho Chi Minh City", "type": "string", "description": "Address on ID card"}, {"key": "idCardIssueDate", "value": "2020-01-15", "type": "string", "description": "ID card issue date (YYYY-MM-DD)"}, {"key": "idCardIssuePlace", "value": "Ho Chi Minh City Police Department", "type": "string", "description": "ID card issue place"}, {"key": "imageAltText", "value": "Uploaded image", "type": "string", "description": "Alt text for uploaded image"}, {"key": "imageAltText1", "value": "First image", "type": "string", "description": "Alt text for first image in bulk upload"}, {"key": "imageAltText2", "value": "Second image", "type": "string", "description": "Alt text for second image in bulk upload"}, {"key": "amenityCategory", "value": "basic", "type": "string", "description": "Amenity category filter (basic, kitchen, bathroom, entertainment, safety, connectivity, building)"}, {"key": "costTypeCategory", "value": "utility", "type": "string", "description": "Cost type category filter (utility, service, parking, maintenance)"}, {"key": "ruleCategory", "value": "smoking", "type": "string", "description": "Rule category filter (smoking, pets, visitors, noise, cleanliness, security, usage, other)"}, {"key": "searchKeyword", "value": "phong tro", "type": "string", "description": "Search keyword for room listings"}, {"key": "searchProvinceId", "value": "79", "type": "string", "description": "Province ID for search filter"}, {"key": "searchDistrictId", "value": "760", "type": "string", "description": "District ID for search filter"}, {"key": "searchWardId", "value": "26734", "type": "string", "description": "Ward ID for search filter"}, {"key": "searchRoomType", "value": "single_room", "type": "string", "description": "Room type for search filter"}, {"key": "searchMinPrice", "value": "1000000", "type": "string", "description": "Minimum price for search filter"}, {"key": "searchMaxPrice", "value": "5000000", "type": "string", "description": "Maximum price for search filter"}, {"key": "searchMinArea", "value": "15", "type": "string", "description": "Minimum area for search filter"}, {"key": "searchMaxArea", "value": "50", "type": "string", "description": "Maximum area for search filter"}, {"key": "searchAmenities", "value": "1,2,3", "type": "string", "description": "Comma-separated amenity IDs for search filter"}, {"key": "searchMaxOccupancy", "value": "2", "type": "string", "description": "Maximum occupancy for search filter"}, {"key": "searchIsVerified", "value": "true", "type": "string", "description": "Filter verified rooms only"}, {"key": "searchSortBy", "value": "price", "type": "string", "description": "Sort field (price, area, createdAt)"}, {"key": "searchSortOrder", "value": "asc", "type": "string", "description": "Sort order (asc, desc)"}, {"key": "searchPage", "value": "1", "type": "string", "description": "Page number for pagination"}, {"key": "searchLimit", "value": "10", "type": "string", "description": "Items per page for pagination"}, {"key": "roomSlug", "value": "van528-quan-10-phong-101887", "type": "string", "description": "Room slug for getting room details"}, {"key": "addressSearchQuery", "value": "<PERSON>", "type": "string", "description": "Search query for address search (minimum 2 characters)"}, {"key": "imagePath", "value": "example-image.jpg", "type": "string", "description": "Path to uploaded image file (auto-populated from upload responses)"}]}