{"name": "trustay-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "biome check --write . && eslint \"{src,app,components}/**/*.ts\" --fix"}, "dependencies": {"@ckeditor/ckeditor5-react": "^11.0.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-table": "^8.21.3", "axios": "^1.11.0", "ckeditor5": "^46.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.525.0", "next": "^15.4.2", "next-themes": "^0.4.6", "postcss": "^8.5.6", "react": "^19.1.0", "react-day-picker": "^9.8.1", "react-dom": "^19.1.0", "sonner": "^2.0.7", "swiper": "^11.2.10", "tailwind-merge": "^3.3.1", "zustand": "^5.0.6"}, "devDependencies": {"@biomejs/biome": "2.1.2", "@eslint/eslintrc": "^3", "@redux-devtools/extension": "^3.3.0", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "lefthook": "^1.12.2", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "^5"}}